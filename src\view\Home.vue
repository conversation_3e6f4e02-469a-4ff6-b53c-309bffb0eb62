<script setup>
// import DocsAPI from "@onlyoffice/document-editor";
import { onMounted, ref, watch, computed, inject } from "vue";
import { useInitDoc } from "../hooks/useInitDoc/useInitDoc";
import { useThemeStore } from "../stores/theme";
import RightButtonPanel from "../components/RightButtonPanel/index.vue";
import DynamicSidebar from "../components/DynamicSidebar/index.vue";
import DocumentEditor from "../components/DocumentEditor/index.vue";
import TopStatusBar from "../components/TopStatusBar/index.vue";
import { AISTORE_FAILED, AISTORE_LOADING, useAIStore } from "../stores/aiInfo";
import { storeToRefs } from "pinia";
import { useRouter ,useRoute} from "vue-router";


// import { loadScripts } from "../loadScript.js";

console.log(import.meta.env.VITE_API_ENDPOINT);

const { init } = useInitDoc();
const aiStore = useAIStore();
const { showAIPanel, aiLoadingStatus } = storeToRefs(aiStore);

const themeStore = useThemeStore();
const sidebarWidth = 450;
const currentPanel = ref('');
const auth = inject('auth');

const route = useRoute()

// 文档标题
const documentTitle = ref('912717_表小雯_新冠疫情下某大学的集团党金期...');

// 计算主题色
const themeColors = computed(() => themeStore.getThemeColors());

// 监听 showAIPanel 变化，同步 currentPanel 状态
watch(showAIPanel, (newVal) => {
    if (newVal) {
        currentPanel.value = 'chat';
    } else if (currentPanel.value === 'chat') {
        currentPanel.value = '';
    }
});

const handlePanelToggle = (panelType) => {
    if (panelType === 'chat') {
        showAIPanel.value = !showAIPanel.value;
        currentPanel.value = showAIPanel.value ? 'chat' : '';
    } else {
        // 关闭AI面板，打开其他面板
        showAIPanel.value = false;
        currentPanel.value = currentPanel.value === panelType ? '' : panelType;
    }
};

// 文档编辑器相关
const documentEditorRef = ref(null);

const handleFileSelect = () => {
    console.log('打开文件选择器');
    // 这里可以打开文件选择器或导航到文件选择页面
};

const handleDocumentReady = () => {
    console.log('文档已准备就绪');
    // 文档加载完成后的处理
};

// 状态栏事件处理
const handleTitleChange = (newTitle) => {
    documentTitle.value = newTitle;
    console.log('保存标题:', newTitle);
    // 这里可以调用API保存文档标题
};

const handleCreateNew = () => {
    console.log('创建新文档');
    // 这里可以打开新建文档的模态框或导航到新建页面
};

const handleUploadPaper = (file) => {
    console.log('上传文件:', file.name);
    // 处理文件上传逻辑
};

onMounted(async () => {
    // 初始化主题
    themeStore.initTheme();
    themeStore.watchSystemTheme();
    const fileId = route.params.fileId

    try {
        // 直接使用 API 端点 URL，让 init 内部处理文件请求
        const fileUrl = `/api/v1/disk/download/${fileId}`;

        init({
            key: fileId, // 使用文件ID作为 key
            url: fileUrl, // 直接使用 API 端点 URL
            title: "文档", // 可以根据需要动态设置文件名
            fileType: "docx", // 可以根据文件扩展名动态设置
            vkey: `vkey_for_${fileId}`,
            token: "your_jwt_token_if_any", // 如果服务器启用了JWT，这里需要后端生成的 token
            permissions: {
                edit: false, // 设置为 false 以只读模式加载
                download: true,
                print: true,
                review: false,
                comment: false
            },
            user: {
                id: "user-01",
                name: "测试用户"
            }
        });
    } catch (error) {
        console.error('文档加载失败:', error);
        // 可以在这里添加错误提示给用户
    }
})
</script>

<template>
    <div class="editor_box" :class="{ 'dark-mode': themeStore.isDark }">
        <!-- 主内容区域 -->
        <div class="main-container">
            <!-- 左侧内容区域（状态栏 + 编辑器） -->
            <div class="content-area" :style="{
                width: currentPanel ? `calc(100% - ${sidebarWidth}px - 64px)` : 'calc(100% - 64px)'
            }">
                <!-- 顶部状态栏 -->
                <TopStatusBar 
                    :title="documentTitle" 
                    @title-change="handleTitleChange" 
                    @create-new="handleCreateNew"
                    @upload-paper="handleUploadPaper" 
                />
                
                <!-- 文档编辑区域 -->
                <div class="editor-container">
                    <DocumentEditor 
                        ref="documentEditorRef" 
                        @file-select="handleFileSelect"
                        @document-ready="handleDocumentReady" 
                    />
                </div>
            </div>

            <!-- 右侧固定按钮列 -->
            <RightButtonPanel :active-panel="currentPanel" @toggle-panel="handlePanelToggle" />

            <!-- 动态侧边栏 -->
            <DynamicSidebar :visible="currentPanel !== ''" :width="sidebarWidth" :panel-type="currentPanel" />
        </div>
    </div>
</template>

<style scoped lang="scss">
.editor_box {
    display: flex;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    flex-direction: column;
    background-color: var(--theme-bg-primary, #f9f9fb);
    color: var(--theme-text-primary, #333);
    transition: all 0.3s ease;

    // 暗色模式
    &.dark-mode {
        background-color: var(--theme-bg-primary, #1a1a1a);
        color: var(--theme-text-primary, #e0e0e0);
    }
}


.main-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.content-area {
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
}

.editor-container {
    position: relative;
    flex: 1;
    height: calc(100vh - 48px);
}



// 修复滚动条样式
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background: var(--theme-border-secondary, rgba(0, 0, 0, 0.2));
    border-radius: 3px;
    transition: background 0.3s ease;

    &:hover {
        background: var(--theme-primary, rgba(0, 0, 0, 0.3));
    }
}

::-webkit-scrollbar-track {
    background: var(--theme-border-primary, rgba(0, 0, 0, 0.05));
    transition: background 0.3s ease;
}

// 暗色模式下的滚动条
.dark-mode {
    ::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);

        &:hover {
            background: var(--theme-primary, rgba(255, 255, 255, 0.3));
        }
    }

    ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
    }
}
</style>
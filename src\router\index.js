import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/',
            name: 'FileSelectorEntry',
            component: () => import('../view/FileSelectorEntry.vue'),
            meta: {
                title: '文件选择器'
            }
        },
        {
            path: '/editor/:fileId',
            component: () => import('../view/Home.vue'),
            meta: {
                title: '编辑'
            }
        }
    ],
})

router.beforeEach((to, from) => {
    // 设置页面标题
    if (to.meta.title) {
        document.title = to.meta.title
    };

    
    // 检查是否需要登录
    if (!to.meta.noCheckAuth) {
        const accessToken = localStorage.getItem('access_token') || sessionStorage.getItem('access_token')
        if (!accessToken) {
            // 未登录，重定向到首页
            return { path: '/' }
        }
    }
})

export default router